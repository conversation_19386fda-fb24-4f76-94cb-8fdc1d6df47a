import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, Mail, Settings, Heart, Globe, Filter, Clock } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth, useSubscriptionPermission } from '@/contexts/AuthContext';
import { useEmailSubscription } from '@/hooks/useEmailSubscription';
import { useTopics } from '@/hooks/useTopics';

interface EmailSubscriptionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface Platform {
  id: string;
  name: string;
  count?: number;
}

const AVAILABLE_PLATFORMS: Platform[] = [
  { id: 'blog', name: 'Blog' },
  { id: 'reddit', name: 'Reddit' },
  { id: 'twitter-rss', name: 'Twitter RSS' },
  { id: 'youtube', name: 'YouTube' },
  { id: 'xiaohongshu', name: 'Xiaohongshu' },
  { id: 'wechat', name: 'WeChat' },
  { id: 'podcast', name: 'Podcast' }
];

// Common timezones for email delivery
const COMMON_TIMEZONES = [
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
  { value: 'America/Denver', label: 'Mountain Time (MT)' },
  { value: 'America/Chicago', label: 'Central Time (CT)' },
  { value: 'America/New_York', label: 'Eastern Time (ET)' },
  { value: 'Europe/London', label: 'London (GMT/BST)' },
  { value: 'Europe/Paris', label: 'Paris (CET/CEST)' },
  { value: 'Europe/Berlin', label: 'Berlin (CET/CEST)' },
  { value: 'Asia/Tokyo', label: 'Tokyo (JST)' },
  { value: 'Asia/Shanghai', label: 'Shanghai (CST)' },
  { value: 'Asia/Hong_Kong', label: 'Hong Kong (HKT)' },
  { value: 'Asia/Singapore', label: 'Singapore (SGT)' },
  { value: 'Australia/Sydney', label: 'Sydney (AEDT/AEST)' },
];

// Hour options for email delivery (0-23)
const HOUR_OPTIONS = Array.from({ length: 24 }, (_, i) => ({
  value: i,
  label: `${i.toString().padStart(2, '0')}:00`
}));

export const EmailSubscriptionDialog: React.FC<EmailSubscriptionDialogProps> = ({
  open,
  onOpenChange
}) => {
  const { t } = useTranslation();
  const { language } = useLanguage();
  const { toast } = useToast();
  const { topics } = useTopics();
  const { hasPermission, isFree } = useSubscriptionPermission('email_subscription');

  const {
    subscription,
    loading,
    error,
    updateSubscription,
    isAuthenticated
  } = useEmailSubscription();

  const [localSubscription, setLocalSubscription] = useState(subscription);
  const [saving, setSaving] = useState(false);

  // 同步远程状态到本地状态
  useEffect(() => {
    setLocalSubscription(subscription);
  }, [subscription]);

  // 当在英文页面时，确保邮件语言设置为英文
  useEffect(() => {
    if (language === 'en' && localSubscription.language !== 'en') {
      setLocalSubscription(prev => ({
        ...prev,
        language: 'en'
      }));
    }
  }, [language, localSubscription.language]);

  // 处理保存
  const handleSave = async () => {
    if (!isAuthenticated) {
      toast({
        title: t('common.error'),
        description: t('auth.loginRequired'),
        variant: 'destructive',
      });
      return;
    }

    // 检查订阅权限
    if (!hasPermission) {
      toast({
        title: t('emailSubscription.premiumRequired'),
        description: t('emailSubscription.premiumRequiredDesc'),
        variant: 'destructive',
      });
      return;
    }

    setSaving(true);
    try {
      await updateSubscription(localSubscription);
      
      toast({
        title: t('common.success'),
        description: localSubscription.enabled 
          ? t('emailSubscription.subscribeSuccess')
          : t('emailSubscription.unsubscribeSuccess'),
      });
      
      onOpenChange(false);
    } catch (error: any) {
      toast({
        title: t('common.error'),
        description: error.message || t('emailSubscription.updateError'),
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // 处理主题选择
  const handleTopicToggle = (topicId: string, checked: boolean) => {
    setLocalSubscription(prev => ({
      ...prev,
      topics: checked 
        ? [...prev.topics, topicId]
        : prev.topics.filter(id => id !== topicId)
    }));
  };

  // 处理平台选择
  const handlePlatformToggle = (platformId: string, checked: boolean) => {
    setLocalSubscription(prev => ({
      ...prev,
      platforms: checked 
        ? [...prev.platforms, platformId]
        : prev.platforms.filter(id => id !== platformId)
    }));
  };

  // 处理全选主题
  const handleSelectAllTopics = (checked: boolean) => {
    setLocalSubscription(prev => ({
      ...prev,
      topics: checked ? topics.map(t => t.id) : []
    }));
  };

  // 处理全选平台
  const handleSelectAllPlatforms = (checked: boolean) => {
    setLocalSubscription(prev => ({
      ...prev,
      platforms: checked ? AVAILABLE_PLATFORMS.map(p => p.id) : []
    }));
  };

  const isAllTopicsSelected = topics.length > 0 && localSubscription.topics.length === topics.length;
  const isAllPlatformsSelected = localSubscription.platforms.length === AVAILABLE_PLATFORMS.length;

  if (!isAuthenticated) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              {t('emailSubscription.title')}
            </DialogTitle>
          </DialogHeader>
          <div className="text-center py-6">
            <p className="text-muted-foreground mb-4">{t('auth.loginRequired')}</p>
            <Button onClick={() => onOpenChange(false)}>
              {t('common.close')}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            {t('emailSubscription.title')}
          </DialogTitle>
          <DialogDescription>
            {t('emailSubscription.description')}
          </DialogDescription>
        </DialogHeader>

        {loading && !localSubscription ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        ) : (
          <div className="space-y-6">
            {/* 启用/禁用订阅 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  {t('emailSubscription.basicSettings')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="email-enabled" className="text-base font-medium">
                      {t('emailSubscription.enableEmail')}
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {t('emailSubscription.enableEmailDesc')}
                    </p>
                  </div>
                  <Switch
                    id="email-enabled"
                    checked={localSubscription.enabled}
                    onCheckedChange={(checked) => 
                      setLocalSubscription(prev => ({ ...prev, enabled: checked }))
                    }
                  />
                </div>

                {localSubscription.enabled && (
                  <div className="space-y-4 pt-4 border-t">
                    {/* 语言选择 - 根据当前页面语言显示不同选项 */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium flex items-center gap-2">
                        <Globe className="h-4 w-4" />
                        {t('emailSubscription.emailLanguage')}
                      </Label>
                      <div className="flex gap-4">
                        {language === 'zh' ? (
                          // 中文页面：显示两个选项
                          <>
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="lang-zh"
                                checked={localSubscription.language === 'zh'}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    setLocalSubscription(prev => ({ ...prev, language: 'zh' }));
                                  }
                                }}
                              />
                              <Label htmlFor="lang-zh" className="text-sm">
                                中文邮件 (包含所有数据源的英文摘要)
                              </Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="lang-en"
                                checked={localSubscription.language === 'en'}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    setLocalSubscription(prev => ({ ...prev, language: 'en' }));
                                  }
                                }}
                              />
                              <Label htmlFor="lang-en" className="text-sm">
                                English Email (English sources only)
                              </Label>
                            </div>
                          </>
                        ) : (
                          // 英文页面：只显示英文选项
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="lang-en"
                              checked={localSubscription.language === 'en'}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setLocalSubscription(prev => ({ ...prev, language: 'en' }));
                                }
                              }}
                            />
                            <Label htmlFor="lang-en" className="text-sm">
                              English Email (English sources only)
                            </Label>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 时区选择 */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium flex items-center gap-2">
                        <Globe className="h-4 w-4" />
                        {t('emailSubscription.timezone')}
                      </Label>
                      <Select
                        value={localSubscription.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone}
                        onValueChange={(value) =>
                          setLocalSubscription(prev => ({ ...prev, timezone: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={t('emailSubscription.selectTimezone')} />
                        </SelectTrigger>
                        <SelectContent>
                          {COMMON_TIMEZONES.map(tz => (
                            <SelectItem key={tz.value} value={tz.value}>
                              {tz.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* 发送时间选择 */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        {t('emailSubscription.sendTime')}
                      </Label>
                      <Select
                        value={localSubscription.send_hour?.toString() || '8'}
                        onValueChange={(value) =>
                          setLocalSubscription(prev => ({ ...prev, send_hour: parseInt(value) }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={t('emailSubscription.selectSendTime')} />
                        </SelectTrigger>
                        <SelectContent>
                          {HOUR_OPTIONS.map(hour => (
                            <SelectItem key={hour.value} value={hour.value.toString()}>
                              {hour.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <p className="text-xs text-muted-foreground">
                        {t('emailSubscription.sendTimeDesc')}
                      </p>
                    </div>

                    {/* 只要收藏的数据源 */}
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <Label htmlFor="favorites-only" className="text-sm font-medium flex items-center gap-2">
                          <Heart className="h-4 w-4" />
                          {t('emailSubscription.favoritesOnly')}
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          {t('emailSubscription.favoritesOnlyDesc')}
                        </p>
                      </div>
                      <Switch
                        id="favorites-only"
                        checked={localSubscription.favorites_only}
                        onCheckedChange={(checked) =>
                          setLocalSubscription(prev => ({ ...prev, favorites_only: checked }))
                        }
                      />
                    </div>

                    {/* 包含播客摘要 */}
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <Label htmlFor="podcast-enabled" className="text-sm font-medium flex items-center gap-2">
                          🎧 {t('emailSubscription.podcastEnabled')}
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          {t('emailSubscription.podcastEnabledDesc')}
                        </p>
                      </div>
                      <Switch
                        id="podcast-enabled"
                        checked={localSubscription.podcast || false}
                        onCheckedChange={(checked) =>
                          setLocalSubscription(prev => ({ ...prev, podcast: checked }))
                        }
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 主题和平台选择 */}
            {localSubscription.enabled && !localSubscription.favorites_only && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Filter className="h-4 w-4" />
                    {t('emailSubscription.contentFilters')}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* 主题选择 */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label className="text-sm font-medium">{t('emailSubscription.topics')}</Label>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="select-all-topics"
                          checked={isAllTopicsSelected}
                          onCheckedChange={handleSelectAllTopics}
                        />
                        <Label htmlFor="select-all-topics" className="text-xs">
                          {t('emailSubscription.selectAll')}
                        </Label>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      {topics.map(topic => (
                        <div key={topic.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`topic-${topic.id}`}
                            checked={localSubscription.topics.includes(topic.id)}
                            onCheckedChange={(checked) => handleTopicToggle(topic.id, checked as boolean)}
                          />
                          <Label htmlFor={`topic-${topic.id}`} className="text-sm">
                            {topic.name}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 平台选择 */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label className="text-sm font-medium">{t('emailSubscription.platforms')}</Label>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="select-all-platforms"
                          checked={isAllPlatformsSelected}
                          onCheckedChange={handleSelectAllPlatforms}
                        />
                        <Label htmlFor="select-all-platforms" className="text-xs">
                          {t('emailSubscription.selectAll')}
                        </Label>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      {AVAILABLE_PLATFORMS.map(platform => (
                        <div key={platform.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`platform-${platform.id}`}
                            checked={localSubscription.platforms.includes(platform.id)}
                            onCheckedChange={(checked) => handlePlatformToggle(platform.id, checked as boolean)}
                          />
                          <Label htmlFor={`platform-${platform.id}`} className="text-sm">
                            {platform.name}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 操作按钮 */}
            <div className="flex justify-end gap-3">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                {t('common.cancel')}
              </Button>
              <Button
                onClick={handleSave}
                disabled={saving || (isFree && !hasPermission)}
                variant={isFree && !hasPermission ? "outline" : "default"}
              >
                {saving && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                {isFree && !hasPermission ? t('emailSubscription.upgradeToPremium') : t('common.save')}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
