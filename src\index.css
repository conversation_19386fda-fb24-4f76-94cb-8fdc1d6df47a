@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* 活力社交风配色 - 橙粉青主题 */
    --background: 30 100% 98%;
    --foreground: 20 15% 15%;

    --card: 30 100% 99%; /* 非常浅的暖色调，几乎透明 */
    --card-foreground: 20 15% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 20 15% 15%;

    --primary: 15 90% 55%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 15 100% 65%;

    --secondary: 30 100% 96%;
    --secondary-foreground: 20 15% 20%;

    --muted: 30 50% 95%;
    --muted-foreground: 20 10% 50%;

    --accent: 315 85% 90%;
    --accent-foreground: 315 85% 25%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 30 50% 90%;
    --input: 30 50% 90%;
    --ring: 15 90% 55%;

    /* 活力社交设计系统 */
    --gradient-primary: linear-gradient(135deg, hsl(15 90% 55%), hsl(315 85% 60%));
    --gradient-secondary: linear-gradient(135deg, hsl(195 100% 70%), hsl(315 85% 75%));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(30 100% 98%));
    --gradient-vibrant: linear-gradient(135deg, hsl(15 90% 55%), hsl(195 100% 60%), hsl(315 85% 60%));
    
    --shadow-elegant: 0 4px 25px -4px hsl(15 90% 55% / 0.15);
    --shadow-card: 0 3px 15px -3px hsl(315 85% 60% / 0.1);
    --shadow-glow: 0 0 40px hsl(15 90% 55% / 0.25);

    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-spring: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    --transition-bounce: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    
    /* 磨砂玻璃效果 */
    --glass-effect: backdrop-blur(20px) saturate(180%);
    --glass-border: rgba(255, 255, 255, 0.125);
    
    /* 悬浮效果 */
    --hover-lift: 0 10px 40px -10px hsl(15 90% 55% / 0.3);
    --hover-glow: 0 0 20px hsl(315 85% 60% / 0.4);
    
    /* 脉冲动画 */
    --pulse-primary: 0 0 0 0 hsl(15 90% 55% / 0.7);
    --pulse-secondary: 0 0 0 0 hsl(315 85% 60% / 0.7);

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 6%; /* 稍微亮一点，但仍然很接近背景 */
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* 现代化交互效果 */
  .interactive-card {
    @apply transition-all duration-300 ease-out transform-gpu;
    @apply hover:scale-[1.02] hover:-translate-y-2;
    @apply hover:shadow-[var(--hover-lift)];
    @apply active:scale-[0.98];
  }

  .glass-effect {
    backdrop-filter: var(--glass-effect);
    border: 1px solid var(--glass-border);
    background: rgba(255, 255, 255, 0.08);
  }

  .pulse-button {
    @apply relative overflow-hidden;
  }

  .floating-element {
    /* 移除持续浮动动画 */
  }

  .shimmer {
    background: linear-gradient(90deg, 
      hsl(0 0% 100% / 0) 0%, 
      hsl(0 0% 100% / 0.2) 50%, 
      hsl(0 0% 100% / 0) 100%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  .magnetic {
    transition: transform 0.2s ease-out;
  }

  /* 文本截断工具类 */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* 响应式卡片优化 */
  .hover-card {
    @apply transition-all duration-200 ease-out;
  }

  .hover-card:hover {
    @apply shadow-lg scale-[1.01];
  }

  /* 透明卡片样式 */
  .card-transparent {
    background: transparent !important;
    border: 1px solid hsl(var(--border) / 0.3) !important;
    box-shadow: none !important;
  }

  .card-minimal {
    background: hsl(var(--background)) !important;
    border: 1px solid hsl(var(--border) / 0.5) !important;
    box-shadow: none !important;
  }

  .card-subtle {
    background: hsl(var(--muted) / 0.3) !important;
    border: 1px solid hsl(var(--border) / 0.4) !important;
    box-shadow: 0 1px 3px 0 hsl(var(--foreground) / 0.05) !important;
  }

  /* 小屏幕优化 */
  @media (max-width: 640px) {
    .container {
      @apply px-3;
    }

    .card-compact {
      @apply p-3;
    }
  }

  /* 移动端布局修复 - 最大化屏幕利用率 */
  @media (max-width: 768px) {
    /* 防止水平滚动 */
    body {
      overflow-x: hidden;
    }

    /* 确保所有容器不超出视口宽度，极小化边距 */
    .max-w-7xl {
      max-width: 100vw;
      padding-left: 0.0625rem; /* 1px */
      padding-right: 0.0625rem; /* 1px */
    }

    /* 移动端卡片内边距优化 - 增加文字与边框距离 */
    .mobile-card-padding {
      @apply px-3 py-2; /* 增加左右内边距让文字离边框更远 */
    }

    /* 移动端按钮组布局 */
    .mobile-button-group {
      @apply flex flex-wrap gap-1 items-center; /* 减少间距 */
    }

    /* 移动端文字大小优化 - 保持适当行间距 */
    .mobile-text-base {
      font-size: 16px;
      line-height: 1.5; /* 保持舒适的行间距 */
    }

    .mobile-text-sm {
      font-size: 14px;
      line-height: 1.4; /* 保持舒适的行间距 */
    }

    .mobile-text-xs {
      font-size: 13px;
      line-height: 1.3; /* 保持舒适的行间距 */
    }

    /* 移动端收藏按钮优化 */
    .mobile-favorite-button {
      @apply px-1 py-1 text-xs min-w-0 flex-shrink-0; /* 减少内边距 */
      max-width: calc(50% - 0.125rem);
    }

    /* 移动端内容区域优化 - 极小化边距 */
    .mobile-content-area {
      @apply px-0; /* 无边距 */
      width: 100%;
      max-width: 100vw;
      box-sizing: border-box;
    }

    /* 移动端导航栏优化 */
    .mobile-navbar {
      @apply px-1; /* 极小导航栏边距 */
    }

    /* 移动端卡片间距优化 */
    .mobile-card-spacing {
      @apply space-y-2; /* 减少卡片间距 */
    }

    /* 移动端页面内边距优化 */
    .mobile-page-padding {
      @apply py-2; /* 减少页面上下边距 */
    }

    /* 移动端标题优化 */
    .mobile-title {
      @apply mb-2; /* 减少标题下边距 */
    }

    /* 移动端统计卡片优化 */
    .mobile-stats-card {
      @apply p-2; /* 减少统计卡片内边距 */
    }

    /* 移动端过滤器优化 */
    .mobile-filters {
      @apply p-2; /* 减少过滤器内边距 */
    }

    /* 移动端Badge间距优化 */
    .mobile-badge-spacing {
      @apply gap-1; /* 减少Badge间距 */
    }

    /* 移动端按钮间距优化 */
    .mobile-button-spacing {
      @apply gap-1; /* 减少按钮间距 */
    }

    /* 移动端表单间距优化 */
    .mobile-form-spacing {
      @apply space-y-2; /* 减少表单元素间距 */
    }

    /* 移动端网格间距优化 */
    .mobile-grid-gap {
      @apply gap-1; /* 减少网格间距 */
    }

    /* 移动端内容最大化 */
    .mobile-maximize-content {
      @apply px-1 py-1; /* 最小化所有边距 */
    }

    /* 移动端极致优化 - 95%屏幕利用率 */
    body {
      margin: 0;
      padding: 0;
    }

    /* 移动端容器极致优化 - 无边距 */
    .mobile-ultra-compact {
      margin: 0 !important;
      padding: 0 !important; /* 完全无边距 */
    }

    /* 移动端卡片极致优化 */
    .mobile-ultra-card {
      margin: 0.125rem 0 !important; /* 2px 上下边距 */
      padding: 0.5rem !important; /* 8px 内边距 - 增加文字与边框距离 */
    }

    /* 移动端文本极致优化 - 保持可读性 */
    .mobile-ultra-text {
      margin: 0 !important;
      padding: 0 !important;
      line-height: 1.4 !important; /* 增加行间距保持可读性 */
    }

    /* 移动端按钮极致优化 */
    .mobile-ultra-button {
      padding: 0.125rem 0.25rem !important; /* 2px 4px */
      margin: 0.125rem !important; /* 2px */
      min-height: auto !important;
    }

    /* 移动端Badge极致优化 */
    .mobile-ultra-badge {
      padding: 0.125rem 0.25rem !important; /* 2px 4px */
      margin: 0.125rem !important; /* 2px */
      font-size: 0.75rem !important; /* 12px */
    }

    /* 移动端Markdown内容优化 - 保持可读性，缩小间距一半 */
    .mobile-markdown-content {
      line-height: 1.7 !important; /* 保持普通文字行间距不变 */
      margin-bottom: 0.25rem !important; /* 从0.5rem缩小到0.25rem */
    }

    .mobile-markdown-content p {
      margin-bottom: 0.25rem !important; /* 从0.5rem缩小到0.25rem */
      line-height: 1.7 !important; /* 保持普通文字行间距不变 */
    }

    .mobile-markdown-content ul,
    .mobile-markdown-content ol {
      margin-bottom: 0.25rem !important; /* 从0.5rem缩小到0.25rem */
      margin-top: 0.125rem !important; /* 从0.25rem缩小到0.125rem */
      padding-left: 1rem !important;
    }

    .mobile-markdown-content li {
      line-height: 1.6 !important; /* 保持列表项行间距不变 */
      margin-bottom: 0.125rem !important; /* 从0.25rem缩小到0.125rem */
    }

    /* 移动端标题和内容区域极致优化 */
    .mobile-title-ultra {
      margin-left: 0.25rem !important; /* 4px 左边距 */
      margin-right: 0.25rem !important; /* 4px 右边距 */
    }

    .mobile-content-ultra {
      margin-left: 0.125rem !important; /* 2px 左边距 */
      margin-right: 0.125rem !important; /* 2px 右边距 */
      padding-left: 0.5rem !important; /* 8px 左内边距 - 增加文字离边框距离 */
      padding-right: 0.5rem !important; /* 8px 右内边距 - 增加文字离边框距离 */
    }

    /* 移动端边框扩展 - 让边框线更贴近屏幕边缘但保持可见 */
    .mobile-border-expand {
      margin-left: -0.25rem !important; /* 向左扩展4px */
      margin-right: -0.25rem !important; /* 向右扩展4px */
      border-radius: 0.25rem !important; /* 保持小圆角 */
    }

    /* 移动端卡片边框扩展 */
    .mobile-card-expand {
      margin-left: -0.125rem !important; /* 向左扩展2px */
      margin-right: -0.125rem !important; /* 向右扩展2px */
      border-radius: 0.25rem !important; /* 保持小圆角 */
    }

    /* 移动端文字内边距优化 */
    .mobile-text-padding {
      padding-left: 0.75rem !important; /* 12px 左内边距 */
      padding-right: 0.75rem !important; /* 12px 右内边距 */
    }
  }

  .text-gradient {
    @apply bg-gradient-primary bg-clip-text text-transparent;
  }

  .blur-fade-in {
    animation: blur-fade-in 0.6s ease-out forwards;
  }

  .bounce-in {
    animation: bounce-in 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
  }

  .slide-in-bottom {
    animation: slide-in-bottom 0.6s ease-out forwards;
  }

  .rotate-in {
    animation: rotate-in 0.5s ease-out forwards;
  }

  /* 现代化按钮效果 */
  .modern-button {
    @apply relative overflow-hidden rounded-xl px-6 py-3;
    @apply bg-gradient-primary text-primary-foreground;
    @apply transition-all duration-300 ease-out;
    @apply hover:shadow-glow hover:scale-105;
    @apply active:scale-95;
  }

  .modern-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  .modern-button:hover::before {
    left: 100%;
  }

  /* 卡片悬浮效果 */
  .hover-card {
    @apply transition-all duration-300 ease-out;
    @apply hover:shadow-glow hover:-translate-y-1;
    @apply hover:bg-gradient-card;
  }

  /* 渐变边框 */
  .gradient-border {
    background: linear-gradient(hsl(var(--background)), hsl(var(--background))) padding-box,
                var(--gradient-primary) border-box;
    border: 2px solid transparent;
  }

  /* Markdown 内容样式 */
  .markdown-content {
    @apply text-sm leading-relaxed;
    line-height: 1.8 !important; /* 保持普通文字行间距不变 */
  }

  /* Markdown 段落间距 - 缩小一半 */
  .markdown-content p {
    margin-bottom: 0.3rem !important; /* 从0.6rem缩小到0.3rem */
    line-height: 1.8 !important; /* 保持普通文字行间距不变 */
  }

  /* Markdown 列表间距 - 缩小一半 */
  .markdown-content ul,
  .markdown-content ol {
    margin-bottom: 0.3rem !important; /* 从0.6rem缩小到0.3rem */
    margin-top: 0.15rem !important; /* 从0.3rem缩小到0.15rem */
  }

  /* 所有摘要页面字体放大1.1倍 */
  .content-summary-enlarged {
    font-size: 1.1em;
  }

  .content-summary-enlarged .mobile-text-base {
    font-size: calc(16px * 1.1);
  }

  .content-summary-enlarged .mobile-text-sm {
    font-size: calc(14px * 1.1);
  }

  .content-summary-enlarged .mobile-text-xs {
    font-size: calc(13px * 1.1);
  }

  .content-summary-enlarged .text-xs {
    font-size: calc(0.75rem * 1.1);
  }

  .content-summary-enlarged .text-sm {
    font-size: calc(0.875rem * 1.1);
  }

  .content-summary-enlarged .text-base {
    font-size: calc(1rem * 1.1);
  }

  .content-summary-enlarged .text-lg {
    font-size: calc(1.125rem * 1.1);
  }

  .content-summary-enlarged .text-xl {
    font-size: calc(1.25rem * 1.1);
  }

  .content-summary-enlarged .text-2xl {
    font-size: calc(1.5rem * 1.1);
  }

  .content-summary-enlarged .text-3xl {
    font-size: calc(1.875rem * 1.1);
  }

  .content-summary-enlarged .text-4xl {
    font-size: calc(2.25rem * 1.1);
  }

  .content-summary-enlarged .mobile-ultra-badge {
    font-size: calc(0.75rem * 1.1) !important;
  }

  /* 标题间距 - 缩小一半 */
  .markdown-content h1 {
    @apply text-2xl font-bold;
    margin-top: 1rem !important; /* 从mt-8(2rem)缩小到1rem */
    margin-bottom: 0.5rem !important; /* 从mb-4(1rem)缩小到0.5rem */
  }

  .markdown-content h2 {
    @apply text-xl font-bold;
    margin-top: 0.75rem !important; /* 从mt-6(1.5rem)缩小到0.75rem */
    margin-bottom: 0.375rem !important; /* 从mb-3(0.75rem)缩小到0.375rem */
  }

  .markdown-content h3 {
    @apply text-lg font-semibold;
    margin-top: 0.5rem !important; /* 从mt-4(1rem)缩小到0.5rem */
    margin-bottom: 0.25rem !important; /* 从mb-2(0.5rem)缩小到0.25rem */
  }

  /* 列表项间距 - 缩小一半 */
  .markdown-content li {
    margin-bottom: 0.125rem !important; /* 从mb-1(0.25rem)缩小到0.125rem */
    line-height: 1.7 !important; /* 保持列表项行间距不变 */
  }

  /* 确保列表项的缩进样式生效，覆盖任何默认样式 */
  .markdown-content li.ml-6 {
    margin-left: 1.5rem !important;
  }

  .markdown-content li.ml-12 {
    margin-left: 3rem !important;
  }

  .markdown-content li.ml-18 {
    margin-left: 4.5rem !important;
  }

  .markdown-content li.ml-24 {
    margin-left: 6rem !important;
  }

  .markdown-content strong {
    @apply font-semibold;
  }

  .markdown-content em {
    @apply italic;
  }

  .markdown-content a {
    @apply text-blue-600 hover:text-blue-800 underline;
  }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 5px hsl(15 90% 55% / 0.3); }
  50% { box-shadow: 0 0 20px hsl(15 90% 55% / 0.6), 0 0 30px hsl(315 85% 60% / 0.4); }
}

@keyframes pulse-ring {
  0% { width: 0; height: 0; opacity: 1; }
  100% { width: 200px; height: 200px; opacity: 0; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes blur-fade-in {
  0% {
    opacity: 0;
    filter: blur(10px);
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    filter: blur(0);
    transform: translateY(0);
  }
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(50px);
  }
  50% {
    opacity: 1;
    transform: scale(1.05) translateY(-10px);
  }
  70% {
    transform: scale(0.95) translateY(0);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes slide-in-bottom {
  0% {
    opacity: 0;
    transform: translateY(100px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes rotate-in {
  0% {
    opacity: 0;
    transform: rotate(-10deg) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}